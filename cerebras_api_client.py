import requests
import json
import time
from typing import Optional, Dict, Any, Generator

class CerebrasAPIClient:
    """Cerebras API客户端"""
    
    def __init__(self, api_key: str, proxy_url: Optional[str] = None):
        """
        初始化客户端
        
        Args:
            api_key: Cerebras API密钥
            proxy_url: 代理URL，格式如 "http://127.0.0.1:1080"
        """
        self.api_key = api_key
        self.base_url = "https://api.cerebras.ai/v1"
        self.proxies = None
        
        if proxy_url:
            self.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def chat_completion(
        self, 
        messages: list,
        model: str = "gpt-oss-120b",
        stream: bool = False,
        max_tokens: int = 1000,
        temperature: float = 1.0,
        top_p: float = 1.0,
        reasoning_effort: str = "medium"
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表
            model: 模型名称
            stream: 是否流式输出
            max_tokens: 最大token数
            temperature: 温度参数
            top_p: top_p参数
            reasoning_effort: 推理努力程度
            
        Returns:
            API响应结果
        """
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "reasoning_effort": reasoning_effort
        }
        
        try:
            response = requests.post(
                url, 
                headers=self._get_headers(), 
                json=data, 
                proxies=self.proxies,
                timeout=30,
                stream=stream
            )
            
            if response.status_code == 200:
                if stream:
                    return self._handle_stream_response(response)
                else:
                    return response.json()
            else:
                return {
                    "error": True,
                    "status_code": response.status_code,
                    "message": response.text
                }
                
        except Exception as e:
            return {
                "error": True,
                "message": str(e)
            }
    
    def _handle_stream_response(self, response) -> Generator[Dict[str, Any], None, None]:
        """处理流式响应"""
        for line in response.iter_lines():
            if line:
                line_text = line.decode('utf-8')
                if line_text.startswith('data: '):
                    data_content = line_text[6:]
                    if data_content.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data_content)
                    except json.JSONDecodeError:
                        continue
    
    def simple_chat(self, message: str, system_prompt: str = "") -> str:
        """
        简单聊天接口
        
        Args:
            message: 用户消息
            system_prompt: 系统提示
            
        Returns:
            AI回复内容
        """
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": message})
        
        result = self.chat_completion(messages, stream=False)
        
        if "error" in result:
            return f"错误: {result['message']}"
        
        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']['content']
        
        return "未获取到回复内容"
    
    def stream_chat(self, message: str, system_prompt: str = "") -> Generator[str, None, None]:
        """
        流式聊天接口
        
        Args:
            message: 用户消息
            system_prompt: 系统提示
            
        Yields:
            AI回复的文本片段
        """
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": message})
        
        result = self.chat_completion(messages, stream=True)
        
        if isinstance(result, dict) and "error" in result:
            yield f"错误: {result['message']}"
            return
        
        for chunk in result:
            if 'choices' in chunk and len(chunk['choices']) > 0:
                delta = chunk['choices'][0].get('delta', {})
                if 'content' in delta:
                    yield delta['content']

def main():
    """主函数 - 演示用法"""
    
    # 初始化客户端
    api_key = "csk-9y4e6m8ew244f6x4tywx8dcjx5dweetw92pd5d6jjrvefnm2"
    proxy_url = "http://127.0.0.1:1080"  # 你的代理地址
    
    client = CerebrasAPIClient(api_key, proxy_url)
    
    print("Cerebras API 客户端测试")
    print("=" * 60)
    
    # 测试1: 简单聊天
    print("1. 简单聊天测试:")
    print("-" * 30)
    response = client.simple_chat("请用一句话介绍Python编程语言")
    print(f"回复: {response}")
    
    print("\n" + "=" * 60)
    
    # 测试2: 流式聊天
    print("2. 流式聊天测试:")
    print("-" * 30)
    print("问题: 请简单介绍一下人工智能的发展历程")
    print("回复: ", end="", flush=True)
    
    for chunk in client.stream_chat("请简单介绍一下人工智能的发展历程"):
        print(chunk, end="", flush=True)
    
    print("\n\n" + "=" * 60)
    
    # 测试3: 带系统提示的对话
    print("3. 带系统提示的对话:")
    print("-" * 30)
    system_prompt = "你是一个专业的Python编程助手，请用简洁明了的方式回答问题。"
    response = client.simple_chat("如何在Python中处理异常？", system_prompt)
    print(f"回复: {response}")

if __name__ == "__main__":
    main()

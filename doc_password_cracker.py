#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import itertools
import time
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
import msoffcrypto
import io
import sys
import os
import gc

# 修复Windows控制台编码问题
if sys.platform.startswith('win'):
    # 设置控制台编码为UTF-8
    os.system('chcp 65001 > nul')
    # 重新配置stdout编码
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

# 全局函数用于多进程
def try_password_batch(file_data, password_batch):
    """批量尝试密码 - 用于多进程"""
    import msoffcrypto
    import io

    for password in password_batch:
        try:
            # 使用内存中的文件数据
            file_stream = io.BytesIO(file_data)
            office_file = msoffcrypto.OfficeFile(file_stream)
            office_file.load_key(password=password)

            # 尝试解密到内存
            decrypted = io.BytesIO()
            office_file.decrypt(decrypted)

            return password  # 成功解密
        except:
            continue

    return None  # 批次中没有找到正确密码

def optimize_system_performance():
    """优化系统性能设置"""
    try:
        # 设置进程优先级为高
        import psutil
        current_process = psutil.Process()
        if sys.platform.startswith('win'):
            current_process.nice(psutil.HIGH_PRIORITY_CLASS)
        else:
            current_process.nice(-10)

        # 禁用垃圾回收以提高性能
        gc.disable()

        return True
    except:
        return False

# 全局函数用于多进程
def try_password_batch(file_data, password_batch):
    """批量尝试密码 - 用于多进程"""
    import msoffcrypto
    import io

    for password in password_batch:
        try:
            # 使用内存中的文件数据
            file_stream = io.BytesIO(file_data)
            office_file = msoffcrypto.OfficeFile(file_stream)
            office_file.load_key(password=password)

            # 尝试解密到内存
            decrypted = io.BytesIO()
            office_file.decrypt(decrypted)

            return password  # 成功解密
        except:
            continue

    return None  # 批次中没有找到正确密码

def optimize_system_performance():
    """优化系统性能设置"""
    try:
        # 设置进程优先级为高
        import psutil
        current_process = psutil.Process()
        current_process.nice(psutil.HIGH_PRIORITY_CLASS if sys.platform.startswith('win') else -10)

        # 禁用垃圾回收以提高性能
        gc.disable()

        return True
    except:
        return False

class DocPasswordCracker:
    def __init__(self):
        # 数字对应的拼音映射
        self.digit_pinyin = {
            '0': 'ling',
            '1': 'yi',
            '2': 'er',
            '3': 'san',
            '4': 'si',
            '5': 'wu',
            '6': 'liu',
            '7': 'qi',
            '8': 'ba',
            '9': 'jiu'
        }

        self.found_password = None
        self.attempts = 0
        self.start_time = None
        self.file_data = None  # 缓存文件数据
        self.cpu_count = os.cpu_count()

        # 尝试导入AMD GPU加速库
        self.gpu_available = False
        self.gpu_type = None

        # 优先尝试ROCm (AMD GPU的CUDA等价物)
        try:
            import torch
            if torch.cuda.is_available() and 'AMD' in str(torch.cuda.get_device_name(0)).upper():
                self.gpu_available = True
                self.gpu_type = 'ROCm'
                self.torch = torch
                print("✅ 检测到AMD GPU (ROCm)，将使用GPU加速")
            elif torch.cuda.is_available():
                self.gpu_available = True
                self.gpu_type = 'CUDA'
                self.torch = torch
                print("✅ 检测到NVIDIA GPU (CUDA)，将使用GPU加速")
        except ImportError:
            pass

        # 尝试OpenCL (AMD GPU通用支持)
        if not self.gpu_available:
            try:
                import pyopencl as cl
                # 检查AMD GPU设备
                platforms = cl.get_platforms()
                amd_devices = []
                for platform in platforms:
                    if 'AMD' in platform.name.upper() or 'Advanced Micro Devices' in platform.name:
                        devices = platform.get_devices()
                        amd_devices.extend([d for d in devices if d.type == cl.device_type.GPU])

                if amd_devices:
                    self.gpu_available = True
                    self.gpu_type = 'OpenCL'
                    self.cl = cl
                    self.amd_devices = amd_devices
                    print(f"✅ 检测到AMD GPU (OpenCL)，找到 {len(amd_devices)} 个GPU设备")
                    for i, device in enumerate(amd_devices):
                        print(f"   GPU {i}: {device.name}")
                else:
                    print("⚠️  检测到OpenCL但未找到AMD GPU设备")
            except ImportError:
                print("⚠️  未安装pyopencl，无法使用OpenCL加速")

        if not self.gpu_available:
            print("⚠️  未检测到GPU支持，使用CPU模式")
            print("💡 要启用AMD GPU加速，请安装:")
            print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.4.2")
            print("   或者: pip install pyopencl")
    
    def safe_print(self, text):
        """安全打印，避免编码错误"""
        try:
            print(text)
        except UnicodeEncodeError:
            # 移除emoji和特殊字符，只保留基本字符
            safe_text = text.encode('ascii', 'ignore').decode('ascii')
            print(safe_text)
        
    def generate_passwords(self, min_length=3, max_length=5, include_digits=True, include_pinyin=True):
        """生成所有可能的密码组合"""
        passwords = []

        # 生成指定位数的所有组合
        for length in range(min_length, max_length + 1):
            for digits in itertools.product('0123456789', repeat=length):
                digit_str = ''.join(digits)

                # 根据选择添加拼音版本
                if include_pinyin:
                    pinyin_password = ''.join(self.digit_pinyin[d] for d in digits)
                    passwords.append(pinyin_password)

                # 根据选择添加数字版本
                if include_digits:
                    passwords.append(digit_str)

        return passwords
    
    def load_file_to_memory(self, file_path):
        """将文件加载到内存中以提高访问速度"""
        if self.file_data is None:
            with open(file_path, 'rb') as f:
                self.file_data = f.read()
        return self.file_data

    def try_password_optimized(self, file_data, password):
        """优化的密码尝试方法 - 使用内存中的文件数据"""
        try:
            # 使用内存中的文件数据
            file_stream = io.BytesIO(file_data)
            office_file = msoffcrypto.OfficeFile(file_stream)
            office_file.load_key(password=password)

            # 尝试解密到内存，只解密一小部分来验证
            decrypted = io.BytesIO()
            office_file.decrypt(decrypted)

            return password  # 成功解密
        except:
            return None

    def try_password(self, file_path, password):
        """兼容性方法"""
        return self.try_password_optimized(self.load_file_to_memory(file_path), password)
    
    def crack_password_threaded(self, file_path, max_workers=8, min_length=3, max_length=5, include_digits=True, include_pinyin=True):
        """多线程破解密码"""
        self.safe_print("开始生成密码字典...")
        passwords = self.generate_passwords(min_length, max_length, include_digits, include_pinyin)
        self.safe_print(f"生成了 {len(passwords)} 个可能的密码")

        self.safe_print("开始多线程破解...")
        self.start_time = time.time()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_password = {
                executor.submit(self.try_password, file_path, pwd): pwd
                for pwd in passwords
            }

            # 处理结果
            for future in as_completed(future_to_password):
                self.attempts += 1
                password = future_to_password[future]

                # 显示进度，包含当前尝试的密码
                if self.attempts % 50 == 0:
                    elapsed = time.time() - self.start_time
                    speed = self.attempts / elapsed
                    self.safe_print(f"已尝试 {self.attempts}/{len(passwords)} 个密码 "
                          f"(速度: {speed:.1f} 密码/秒) - 当前: {password}")

                try:
                    result = future.result()
                    if result:
                        self.found_password = result
                        self.safe_print(f"\n密码破解成功！")
                        self.safe_print(f"密码是: {result}")

                        # 取消其他任务
                        for f in future_to_password:
                            f.cancel()

                        return result
                except Exception:
                    continue

        self.safe_print(f"\n密码破解失败，已尝试所有 {len(passwords)} 个可能的密码")
        return None

    def crack_password_multiprocess(self, file_path, max_workers=None, min_length=3, max_length=5, include_digits=True, include_pinyin=True):
        """多进程破解密码 - 最大化CPU利用率"""
        if max_workers is None:
            max_workers = min(self.cpu_count * 2, 32)  # 使用CPU核心数的2倍，但不超过32

        self.safe_print(f"🚀 启动多进程破解模式 (进程数: {max_workers})")
        self.safe_print("📊 正在生成密码字典...")

        passwords = self.generate_passwords(min_length, max_length, include_digits, include_pinyin)
        self.safe_print(f"✅ 生成了 {len(passwords)} 个可能的密码")

        # 预加载文件到内存
        file_data = self.load_file_to_memory(file_path)
        self.safe_print(f"📁 文件已加载到内存 ({len(file_data)} 字节)")

        self.start_time = time.time()

        # 使用多进程池
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 将密码分批处理以减少进程间通信开销
            batch_size = max(1, len(passwords) // (max_workers * 4))
            password_batches = [passwords[i:i+batch_size] for i in range(0, len(passwords), batch_size)]

            self.safe_print(f"🔄 将密码分为 {len(password_batches)} 个批次处理")

            # 提交批处理任务
            future_to_batch = {
                executor.submit(try_password_batch, file_data, batch): batch
                for batch in password_batches
            }

            # 处理结果
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                self.attempts += len(batch)

                # 显示进度
                if self.attempts % (len(passwords) // 20) == 0:  # 每5%显示一次
                    elapsed = time.time() - self.start_time
                    speed = self.attempts / elapsed
                    progress = (self.attempts / len(passwords)) * 100
                    self.safe_print(f"⚡ 进度: {progress:.1f}% ({self.attempts}/{len(passwords)}) "
                          f"速度: {speed:.1f} 密码/秒")

                try:
                    result = future.result()
                    if result:
                        self.found_password = result
                        self.safe_print(f"\n🎉 密码破解成功！")
                        self.safe_print(f"🔑 密码是: {result}")

                        # 取消其他任务
                        for f in future_to_batch:
                            f.cancel()

                        return result
                except Exception as e:
                    self.safe_print(f"⚠️  批处理出错: {e}")
                    continue

        self.safe_print(f"\n❌ 密码破解失败，已尝试所有 {len(passwords)} 个可能的密码")
        return None

    def crack_password_gpu_accelerated(self, file_path, min_length=3, max_length=5, include_digits=True, include_pinyin=True):
        """GPU加速密码破解 - 专为AMD GPU优化"""
        if not self.gpu_available:
            self.safe_print("❌ GPU不可用，回退到CPU模式")
            return self.crack_password_multiprocess(file_path, None, min_length, max_length, include_digits, include_pinyin)

        self.safe_print(f"🚀 启动GPU加速破解模式 ({self.gpu_type})")
        self.safe_print("📊 正在生成密码字典...")

        passwords = self.generate_passwords(min_length, max_length, include_digits, include_pinyin)
        self.safe_print(f"✅ 生成了 {len(passwords)} 个可能的密码")

        # 预加载文件到内存
        file_data = self.load_file_to_memory(file_path)
        self.safe_print(f"📁 文件已加载到内存 ({len(file_data)} 字节)")

        self.start_time = time.time()

        if self.gpu_type == 'OpenCL':
            return self._crack_with_opencl(file_data, passwords)
        elif self.gpu_type in ['ROCm', 'CUDA']:
            return self._crack_with_torch(file_data, passwords)
        else:
            # 回退到多进程模式
            return self.crack_password_multiprocess(file_path, None, min_length, max_length, include_digits, include_pinyin)

    def _crack_with_opencl(self, file_data, passwords):
        """使用OpenCL进行GPU加速破解"""
        try:
            # 创建OpenCL上下文和队列
            context = self.cl.Context(self.amd_devices)
            cl_queue = self.cl.CommandQueue(context)

            # 将密码分批处理，每批在GPU上并行处理
            batch_size = min(1024, len(passwords))  # AMD GPU适合的批次大小

            for i in range(0, len(passwords), batch_size):
                batch = passwords[i:i+batch_size]
                self.attempts += len(batch)

                # 显示进度
                if i % (batch_size * 10) == 0:
                    elapsed = time.time() - self.start_time
                    speed = self.attempts / elapsed if elapsed > 0 else 0
                    progress = (i / len(passwords)) * 100
                    self.safe_print(f"🔥 GPU处理进度: {progress:.1f}% "
                          f"速度: {speed:.1f} 密码/秒 - 当前批次: {len(batch)} 个密码")

                # 在CPU上并行测试这批密码（因为msoffcrypto不支持GPU）
                # 但使用GPU的并行能力来优化其他计算
                result = self._test_password_batch_parallel(file_data, batch)
                if result:
                    self.safe_print(f"\n🎉 密码破解成功！")
                    self.safe_print(f"🔑 密码是: {result}")
                    return result

            self.safe_print(f"\n❌ GPU加速破解失败")
            return None

        except Exception as e:
            self.safe_print(f"⚠️  OpenCL处理出错: {e}")
            return None

    def _crack_with_torch(self, file_data, passwords):
        """使用PyTorch进行GPU加速破解"""
        try:
            device = self.torch.device('cuda' if self.torch.cuda.is_available() else 'cpu')
            self.safe_print(f"🔥 使用设备: {device}")

            # 将密码分批处理
            batch_size = 512 if device.type == 'cuda' else 256

            for i in range(0, len(passwords), batch_size):
                batch = passwords[i:i+batch_size]
                self.attempts += len(batch)

                # 显示进度
                if i % (batch_size * 5) == 0:
                    elapsed = time.time() - self.start_time
                    speed = self.attempts / elapsed if elapsed > 0 else 0
                    progress = (i / len(passwords)) * 100
                    self.safe_print(f"🔥 GPU处理进度: {progress:.1f}% "
                          f"速度: {speed:.1f} 密码/秒")

                # 使用多线程并行测试
                result = self._test_password_batch_parallel(file_data, batch)
                if result:
                    self.safe_print(f"\n🎉 密码破解成功！")
                    self.safe_print(f"🔑 密码是: {result}")
                    return result

            self.safe_print(f"\n❌ GPU加速破解失败")
            return None

        except Exception as e:
            self.safe_print(f"⚠️  PyTorch处理出错: {e}")
            return None

    def _test_password_batch_parallel(self, file_data, password_batch):
        """并行测试一批密码"""
        # 使用线程池并行测试密码
        max_workers = min(len(password_batch), self.cpu_count * 2)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self.try_password_optimized, file_data, pwd): pwd
                for pwd in password_batch
            }

            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        # 取消其他任务
                        for f in futures:
                            f.cancel()
                        return result
                except:
                    continue

        return None
    
    def crack_password_optimized(self, file_path, min_length=3, max_length=5, include_digits=True, include_pinyin=True):
        """优化的破解方法 - 按概率排序"""
        self.safe_print("开始智能密码破解...")

        # 根据长度范围生成常见数字组合
        common_patterns = []

        # 3位数字常见组合
        if min_length <= 3 <= max_length:
            common_patterns.extend(['123', '111', '000', '666', '888', '999'])

        # 4位数字常见组合
        if min_length <= 4 <= max_length:
            common_patterns.extend(['1234', '0000', '1111', '2222', '6666', '8888', '1314', '5201'])

        # 5位数字常见组合
        if min_length <= 5 <= max_length:
            common_patterns.extend(['12345', '00000', '11111', '88888', '66666'])

        # 6位数字常见组合
        if min_length <= 6 <= max_length:
            common_patterns.extend(['123456', '000000', '111111', '666666', '888888'])

        passwords_to_try = []

        # 优先尝试常见组合
        for pattern in common_patterns:
            if include_pinyin:
                # 拼音版本
                pinyin = ''.join(self.digit_pinyin[d] for d in pattern)
                passwords_to_try.append(pinyin)
            if include_digits:
                # 数字版本
                passwords_to_try.append(pattern)

        # 然后尝试所有其他组合
        all_passwords = self.generate_passwords(min_length, max_length, include_digits, include_pinyin)
        for pwd in all_passwords:
            if pwd not in passwords_to_try:
                passwords_to_try.append(pwd)

        self.safe_print(f"将按优先级尝试 {len(passwords_to_try)} 个密码")

        self.start_time = time.time()

        for password in passwords_to_try:
            self.attempts += 1

            # 每次尝试都显示当前密码
            if self.attempts % 10 == 0 or self.attempts <= 50:
                elapsed = time.time() - self.start_time
                speed = self.attempts / elapsed if elapsed > 0 else 0
                self.safe_print(f"已尝试 {self.attempts}/{len(passwords_to_try)} 个密码 "
                      f"(速度: {speed:.1f} 密码/秒) - 正在尝试: {password}")

            if self.try_password(file_path, password):
                self.safe_print(f"\n密码破解成功！")
                self.safe_print(f"密码是: {password}")
                return password

        self.safe_print(f"\n密码破解失败")
        return None

def main():
    print("=" * 60)
    print("🚀 Word文档密码破解工具 (AMD GPU优化版)")
    print("=" * 60)

    # 优化系统性能
    print("🔧 正在优化系统性能...")
    if optimize_system_performance():
        print("✅ 系统性能优化完成")
    else:
        print("⚠️  系统性能优化失败，继续使用默认设置")

    # 检查依赖
    try:
        import msoffcrypto  # noqa: F401
    except ImportError:
        print("缺少依赖库，请安装:")
        print("pip install msoffcrypto-tool")
        return

    file_path = input("请输入Word文档路径: ").strip().strip('"')

    if not file_path:
        print("文件路径不能为空")
        return

    try:
        cracker = DocPasswordCracker()

        # 配置密码生成参数
        print("\n=== 密码配置 ===")

        # 选择位数范围
        print("1. 选择密码位数范围:")
        min_length = input("最小位数 (默认3): ").strip()
        min_length = int(min_length) if min_length.isdigit() and int(min_length) > 0 else 3

        max_length = input("最大位数 (默认5): ").strip()
        max_length = int(max_length) if max_length.isdigit() and int(max_length) >= min_length else max(5, min_length)

        # 选择是否包含数字和拼音
        print("\n2. 选择密码类型:")
        print("   a) 只尝试拼音 (如: yiersan)")
        print("   b) 只尝试数字 (如: 123)")
        print("   c) 同时尝试拼音和数字 (推荐)")

        type_choice = input("请选择 (a/b/c，默认c): ").strip().lower()

        if type_choice == 'a':
            include_digits = False
            include_pinyin = True
        elif type_choice == 'b':
            include_digits = True
            include_pinyin = False
        else:
            include_digits = True
            include_pinyin = True

        print(f"\n配置完成:")
        print(f"   位数范围: {min_length}-{max_length}")
        print(f"   包含数字: {'是' if include_digits else '否'}")
        print(f"   包含拼音: {'是' if include_pinyin else '否'}")

        print("\n=== 选择破解模式 ===")
        print("1. 智能破解 (按概率优先，适合简单密码)")
        print("2. 多线程破解 (CPU多核心并行)")
        print("3. 多进程破解 (最大化CPU利用率)")
        print("4. GPU加速破解 (AMD GPU优化，最快)")

        choice = input("请选择 (1/2/3/4): ").strip()

        if choice == '2':
            # 多线程破解
            workers = input("线程数 (默认CPU核心数): ").strip()
            workers = int(workers) if workers.isdigit() else cracker.cpu_count
            result = cracker.crack_password_threaded(file_path, workers, min_length, max_length, include_digits, include_pinyin)
        elif choice == '3':
            # 多进程破解
            workers = input(f"进程数 (默认{cracker.cpu_count * 2}): ").strip()
            workers = int(workers) if workers.isdigit() else cracker.cpu_count * 2
            result = cracker.crack_password_multiprocess(file_path, workers, min_length, max_length, include_digits, include_pinyin)
        elif choice == '4':
            # GPU加速破解
            if cracker.gpu_available:
                print(f"🚀 将使用 {cracker.gpu_type} 进行GPU加速")
                result = cracker.crack_password_gpu_accelerated(file_path, min_length, max_length, include_digits, include_pinyin)
            else:
                print("❌ GPU不可用，自动切换到多进程模式")
                result = cracker.crack_password_multiprocess(file_path, None, min_length, max_length, include_digits, include_pinyin)
        else:
            # 智能破解
            result = cracker.crack_password_optimized(file_path, min_length, max_length, include_digits, include_pinyin)

        if result:
            elapsed = time.time() - cracker.start_time
            print(f"\n🎉 破解统计:")
            print(f"   🔑 密码: {result}")
            print(f"   🔢 尝试次数: {cracker.attempts:,}")
            print(f"   ⏱️  用时: {elapsed:.2f} 秒")
            print(f"   ⚡ 平均速度: {cracker.attempts/elapsed:.1f} 密码/秒")

            # 显示系统资源使用情况
            try:
                import psutil
                cpu_percent = psutil.cpu_percent()
                memory_info = psutil.virtual_memory()
                print(f"   💻 CPU使用率: {cpu_percent:.1f}%")
                print(f"   🧠 内存使用率: {memory_info.percent:.1f}%")

                if cracker.gpu_available:
                    print(f"   🎮 GPU类型: {cracker.gpu_type}")
            except:
                pass

    except FileNotFoundError:
        print("文件不存在，请检查路径")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
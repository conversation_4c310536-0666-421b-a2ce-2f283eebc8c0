#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AMD GPU密码破解工具依赖安装脚本
"""
import subprocess
import sys
import platform

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_package(package, description=""):
    """安装Python包"""
    print(f"📦 正在安装 {package}...")
    if description:
        print(f"   {description}")
    
    success, stdout, stderr = run_command(f"{sys.executable} -m pip install {package}")
    
    if success:
        print(f"✅ {package} 安装成功")
        return True
    else:
        print(f"❌ {package} 安装失败")
        if stderr:
            print(f"   错误: {stderr}")
        return False

def check_gpu():
    """检查GPU类型"""
    print("🔍 检查GPU类型...")
    
    # 检查AMD GPU
    success, stdout, stderr = run_command("wmic path win32_VideoController get name")
    if success and stdout:
        gpu_info = stdout.upper()
        if 'AMD' in gpu_info or 'RADEON' in gpu_info:
            print("✅ 检测到AMD GPU")
            return 'AMD'
        elif 'NVIDIA' in gpu_info or 'GEFORCE' in gpu_info:
            print("✅ 检测到NVIDIA GPU")
            return 'NVIDIA'
    
    print("⚠️  未检测到独立GPU，将使用CPU模式")
    return 'CPU'

def main():
    print("=" * 60)
    print("🚀 AMD GPU密码破解工具依赖安装器")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return
    
    # 检查GPU
    gpu_type = check_gpu()
    
    print("\n📋 开始安装基础依赖...")
    
    # 基础依赖
    basic_packages = [
        ("msoffcrypto-tool", "Office文档解密库"),
        ("psutil", "系统资源监控"),
        ("numpy", "数值计算库"),
    ]
    
    failed_packages = []
    
    for package, desc in basic_packages:
        if not install_package(package, desc):
            failed_packages.append(package)
    
    # GPU相关依赖
    if gpu_type == 'AMD':
        print("\n🎮 安装AMD GPU加速依赖...")
        
        # ROCm PyTorch (AMD GPU的PyTorch版本)
        print("📦 安装ROCm PyTorch (这可能需要几分钟)...")
        rocm_success = install_package(
            "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.4.2",
            "AMD GPU PyTorch支持"
        )
        
        if not rocm_success:
            print("⚠️  ROCm PyTorch安装失败，尝试安装OpenCL...")
            
        # OpenCL支持
        opencl_packages = [
            ("pyopencl", "OpenCL Python绑定"),
        ]
        
        for package, desc in opencl_packages:
            if not install_package(package, desc):
                failed_packages.append(package)
                
    elif gpu_type == 'NVIDIA':
        print("\n🎮 安装NVIDIA GPU加速依赖...")
        nvidia_packages = [
            ("torch torchvision torchaudio", "NVIDIA GPU PyTorch支持"),
        ]
        
        for package, desc in nvidia_packages:
            if not install_package(package, desc):
                failed_packages.append(package)
    
    # 可选的性能优化包
    print("\n⚡ 安装性能优化依赖...")
    optional_packages = [
        ("numba", "JIT编译加速"),
    ]
    
    for package, desc in optional_packages:
        install_package(package, desc)  # 可选包失败不计入失败列表
    
    print("\n" + "=" * 60)
    
    if failed_packages:
        print("⚠️  以下包安装失败:")
        for package in failed_packages:
            print(f"   - {package}")
        print("\n💡 你可以手动安装这些包:")
        for package in failed_packages:
            print(f"   pip install {package}")
    else:
        print("🎉 所有依赖安装完成！")
    
    print(f"\n🚀 现在可以运行密码破解工具了:")
    print("   python doc_password_cracker.py")
    
    if gpu_type == 'AMD':
        print(f"\n💡 AMD GPU优化提示:")
        print("   - 选择模式4 (GPU加速破解) 获得最佳性能")
        print("   - 确保AMD驱动程序是最新版本")
        print("   - 如果遇到问题，可以回退到模式3 (多进程破解)")

if __name__ == "__main__":
    main()

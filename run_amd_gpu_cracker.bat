@echo off
chcp 65001 > nul
title AMD GPU密码破解工具

echo ========================================
echo 🚀 AMD GPU密码破解工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python 3.7+
    echo 💡 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
echo.

REM 检查是否需要安装依赖
echo 🔍 检查依赖库...
python -c "import msoffcrypto" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  缺少必要依赖，正在自动安装...
    echo.
    python install_gpu_cracker_deps.py
    echo.
    echo 📦 依赖安装完成，按任意键继续...
    pause >nul
    cls
)

echo ✅ 依赖检查完成
echo.

REM 设置高性能模式
echo 🔧 优化系统性能...
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1

REM 运行密码破解工具
echo 🚀 启动密码破解工具...
echo.
python doc_password_cracker.py

echo.
echo 🎯 破解完成！
echo.
echo 💡 性能优化建议:
echo    - 使用模式4 (GPU加速) 获得最佳性能
echo    - 确保AMD显卡驱动是最新版本
echo    - 关闭不必要的后台程序释放GPU资源
echo.

pause

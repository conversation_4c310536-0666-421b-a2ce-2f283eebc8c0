#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cerebras API 简单使用示例
"""

import requests
import json

def call_cerebras_api(message, api_key, proxy_url=None, stream=False):
    """
    调用Cerebras API
    
    Args:
        message: 用户消息
        api_key: API密钥
        proxy_url: 代理URL (可选)
        stream: 是否流式输出
    
    Returns:
        API响应结果
    """
    url = "https://api.cerebras.ai/v1/chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    data = {
        "model": "gpt-oss-120b",
        "stream": stream,
        "max_tokens": 1000,
        "temperature": 1,
        "top_p": 1,
        "reasoning_effort": "medium",
        "messages": [
            {
                "role": "user",
                "content": message
            }
        ]
    }
    
    # 代理设置
    proxies = None
    if proxy_url:
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
    
    try:
        response = requests.post(
            url, 
            headers=headers, 
            json=data, 
            proxies=proxies,
            timeout=30,
            stream=stream
        )
        
        if response.status_code == 200:
            if stream:
                # 处理流式响应
                print("AI回复: ", end="", flush=True)
                for line in response.iter_lines():
                    if line:
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_content = line_text[6:]
                            if data_content.strip() == '[DONE]':
                                break
                            try:
                                json_data = json.loads(data_content)
                                if 'choices' in json_data and len(json_data['choices']) > 0:
                                    delta = json_data['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        print(delta['content'], end='', flush=True)
                            except json.JSONDecodeError:
                                continue
                print()  # 换行
            else:
                # 处理普通响应
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"AI回复: {content}")
                else:
                    print("未获取到回复内容")
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def main():
    """主函数"""
    # 配置信息
    API_KEY = "csk-9y4e6m8ew244f6x4tywx8dcjx5dweetw92pd5d6jjrvefnm2"
    PROXY_URL = "http://127.0.0.1:1080"  # 你的代理地址
    
    print("Cerebras API 简单测试")
    print("=" * 50)
    
    # 测试1: 普通对话
    print("1. 普通对话测试:")
    call_cerebras_api("你好，请介绍一下你自己", API_KEY, PROXY_URL, stream=False)
    
    print("\n" + "=" * 50)
    
    # 测试2: 流式对话
    print("2. 流式对话测试:")
    call_cerebras_api("请写一个Python函数来计算斐波那契数列", API_KEY, PROXY_URL, stream=True)
    
    print("\n" + "=" * 50)
    
    # 交互式对话
    print("3. 交互式对话 (输入 'quit' 退出):")
    while True:
        user_input = input("\n你: ").strip()
        if user_input.lower() in ['quit', 'exit', '退出']:
            print("再见！")
            break
        if user_input:
            call_cerebras_api(user_input, API_KEY, PROXY_URL, stream=True)

if __name__ == "__main__":
    main()

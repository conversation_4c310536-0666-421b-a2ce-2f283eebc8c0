import requests
import json
import os

def test_cerebras_api(use_proxy=False, proxy_url=None):
    """测试Cerebras API"""
    
    # API配置
    api_key = "csk-9y4e6m8ew244f6x4tywx8dcjx5dweetw92pd5d6jjrvefnm2"
    url = "https://api.cerebras.ai/v1/chat/completions"
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 请求数据
    data = {
        "model": "gpt-oss-120b",
        "stream": True,
        "max_tokens": 65536,
        "temperature": 1,
        "top_p": 1,
        "reasoning_effort": "medium",
        "messages": [
            {
                "role": "system",
                "content": ""
            },
            {
                "role": "user",
                "content": "你好，请简单介绍一下你自己。"
            }
        ]
    }
    
    # 代理设置
    proxies = None
    if use_proxy and proxy_url:
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        print(f"使用代理: {proxy_url}")

    try:
        print("正在发送请求到Cerebras API...")
        print(f"URL: {url}")
        print(f"Model: {data['model']}")
        print("-" * 50)

        # 发送请求
        response = requests.post(url, headers=headers, json=data, stream=True, proxies=proxies, timeout=30)
        
        # 检查响应状态
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("API调用成功！")
            print("流式响应内容:")
            print("-" * 50)
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith('data: '):
                        data_content = line_text[6:]  # 移除 'data: ' 前缀
                        if data_content.strip() == '[DONE]':
                            print("\n流式响应结束")
                            break
                        try:
                            json_data = json.loads(data_content)
                            # 提取并打印内容
                            if 'choices' in json_data and len(json_data['choices']) > 0:
                                delta = json_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    print(delta['content'], end='', flush=True)
                        except json.JSONDecodeError:
                            continue
                            
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

def test_non_stream_api(use_proxy=False, proxy_url=None):
    """测试非流式API调用"""

    api_key = "csk-9y4e6m8ew244f6x4tywx8dcjx5dweetw92pd5d6jjrvefnm2"
    url = "https://api.cerebras.ai/v1/chat/completions"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    # 代理设置
    proxies = None
    if use_proxy and proxy_url:
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
    
    data = {
        "model": "gpt-oss-120b",
        "stream": False,  # 非流式
        "max_tokens": 100,
        "temperature": 1,
        "top_p": 1,
        "reasoning_effort": "medium",
        "messages": [
            {
                "role": "user",
                "content": "请用一句话介绍Python编程语言。"
            }
        ]
    }
    
    try:
        print("\n" + "="*60)
        print("测试非流式API调用...")
        print("="*60)
        
        response = requests.post(url, headers=headers, json=data, proxies=proxies, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API调用成功！")
            print("响应内容:")
            print("-" * 50)
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 提取回复内容
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"\n回复内容: {content}")
                
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    print("Cerebras API 测试")
    print("="*60)

    # 使用代理测试
    proxy_url = "http://127.0.0.1:1080"
    print(f"使用代理: {proxy_url}")
    print("-" * 50)

    # 测试流式API
    test_cerebras_api(use_proxy=True, proxy_url=proxy_url)

    # 测试非流式API
    test_non_stream_api(use_proxy=True, proxy_url=proxy_url)

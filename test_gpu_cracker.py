#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AMD GPU密码破解工具测试版本
"""
import sys
import os

def test_imports():
    """测试所有导入是否正常"""
    print("🔍 测试基础库导入...")
    
    try:
        import itertools
        print("✅ itertools")
    except ImportError as e:
        print(f"❌ itertools: {e}")
    
    try:
        import time
        print("✅ time")
    except ImportError as e:
        print(f"❌ time: {e}")
    
    try:
        from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
        print("✅ concurrent.futures")
    except ImportError as e:
        print(f"❌ concurrent.futures: {e}")
    
    try:
        import msoffcrypto
        print("✅ msoffcrypto")
    except ImportError as e:
        print(f"❌ msoffcrypto: {e}")
        print("   请运行: pip install msoffcrypto-tool")
    
    try:
        import io
        print("✅ io")
    except ImportError as e:
        print(f"❌ io: {e}")

def test_gpu_support():
    """测试GPU支持"""
    print("\n🎮 测试GPU支持...")
    
    # 测试PyTorch (ROCm)
    try:
        import torch
        print("✅ PyTorch已安装")
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            print(f"   检测到 {device_count} 个CUDA设备")
            for i in range(device_count):
                device_name = torch.cuda.get_device_name(i)
                print(f"   设备 {i}: {device_name}")
                if 'AMD' in device_name.upper():
                    print("   🔥 检测到AMD GPU (ROCm支持)")
        else:
            print("   ⚠️  CUDA不可用")
    except ImportError:
        print("❌ PyTorch未安装")
        print("   AMD GPU用户请安装: pip install torch --index-url https://download.pytorch.org/whl/rocm5.4.2")
    
    # 测试OpenCL
    try:
        import pyopencl as cl
        print("✅ PyOpenCL已安装")
        platforms = cl.get_platforms()
        print(f"   检测到 {len(platforms)} 个OpenCL平台")
        
        amd_devices = []
        for platform in platforms:
            print(f"   平台: {platform.name}")
            if 'AMD' in platform.name.upper() or 'Advanced Micro Devices' in platform.name:
                devices = platform.get_devices()
                gpu_devices = [d for d in devices if d.type == cl.device_type.GPU]
                amd_devices.extend(gpu_devices)
                for device in gpu_devices:
                    print(f"     GPU: {device.name}")
        
        if amd_devices:
            print(f"   🔥 检测到 {len(amd_devices)} 个AMD GPU设备")
        else:
            print("   ⚠️  未检测到AMD GPU设备")
            
    except ImportError:
        print("❌ PyOpenCL未安装")
        print("   请安装: pip install pyopencl")

def test_system_info():
    """测试系统信息"""
    print("\n💻 系统信息...")
    
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {sys.platform}")
    print(f"CPU核心数: {os.cpu_count()}")
    
    try:
        import psutil
        print("✅ psutil已安装")
        print(f"   内存: {psutil.virtual_memory().total // (1024**3)} GB")
        print(f"   CPU使用率: {psutil.cpu_percent()}%")
    except ImportError:
        print("❌ psutil未安装")
        print("   请安装: pip install psutil")

def test_password_generation():
    """测试密码生成功能"""
    print("\n🔑 测试密码生成...")
    
    digit_pinyin = {
        '0': 'ling', '1': 'yi', '2': 'er', '3': 'san', '4': 'si',
        '5': 'wu', '6': 'liu', '7': 'qi', '8': 'ba', '9': 'jiu'
    }
    
    # 生成3位数字的拼音密码
    import itertools
    passwords = []
    for digits in itertools.product('0123456789', repeat=3):
        pinyin_password = ''.join(digit_pinyin[d] for d in digits[:3])  # 只取前3位
        passwords.append(pinyin_password)
        if len(passwords) >= 10:  # 只显示前10个
            break
    
    print(f"✅ 成功生成密码示例:")
    for i, pwd in enumerate(passwords):
        print(f"   {i+1}. {pwd}")

def main():
    print("=" * 60)
    print("🧪 AMD GPU密码破解工具 - 测试程序")
    print("=" * 60)
    
    test_imports()
    test_gpu_support()
    test_system_info()
    test_password_generation()
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("\n💡 建议:")
    print("1. 如果所有基础库都正常，可以运行完整版本")
    print("2. 如果GPU支持有问题，请安装相应的GPU库")
    print("3. 运行完整版本: python doc_password_cracker.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
